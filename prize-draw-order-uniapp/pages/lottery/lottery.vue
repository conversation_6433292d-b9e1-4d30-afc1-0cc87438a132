<template>
  <view class="lottery-container">
    <!-- 商家信息 -->
    <view class="merchant-header" v-if="merchantInfo">
      <view class="merchant-name">{{ merchantInfo.merchantName }}</view>
      <view class="activity-name" v-if="currentActivity">{{ currentActivity.activityName }}</view>
    </view>

    <!-- 抽奖结果展示（用户已抽过奖时显示） -->
    <view class="lottery-result-container" v-if="hasDrawn && lotteryResult">
      <view class="result-wrapper">
        <view class="result-icon">
          <text v-if="lotteryResult.isWinner === '1'">🎉</text>
          <text v-else>😊</text>
        </view>
        <view class="result-title">
          <text v-if="lotteryResult.isWinner === '1'">恭喜中奖！</text>
          <text v-else>谢谢参与</text>
        </view>
        <view class="result-prize">
          <text>{{ lotteryResult.prizeName }}</text>
        </view>
        <view class="result-time">
          <text>抽奖时间：{{ formatTime(lotteryResult.drawTime) }}</text>
        </view>
        <view class="result-status" v-if="lotteryResult.isWinner === '1'">
          <text :class="{ 'claimed': lotteryResult.claimStatus === '1' }">
            {{ lotteryResult.claimStatus === '1' ? '已领取' : '待领取' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 九宫格抽奖（用户未抽过奖时显示） -->
    <view class="lottery-grid-container" v-if="!hasDrawn">
      <view class="grid-wrapper">
        <view class="lottery-grid">
          <view class="grid-item" v-for="(item, index) in gridItems" :key="index"
            :class="{ 'active': currentIndex === index, 'center': index === 4 }"
            @click="index === 4 ? startLottery() : null">
            <view v-if="index === 4" class="center-button">
              <view class="center-text">{{ isDrawing ? '抽奖中...' : '点击抽奖' }}</view>
              <view class="remaining-text">剩余{{ remainingDraws }}次</view>
            </view>
            <view v-else-if="item" class="prize-item">
              <view class="prize-icon">🎁</view>
              <view class="prize-name">{{ item.prizeName }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖品列表 -->
    <view class="prize-list" v-if="prizeList.length > 0">
      <view class="prize-title">奖品设置</view>
      <view class="prize-items">
        <view class="prize-item" v-for="(prize, index) in prizeList" :key="index">
          <view class="prize-name">{{ prize.prizeName }}</view>
          <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
        </view>
      </view>
    </view>

    <!-- 抽奖记录 -->
    <view class="lottery-records">
      <view class="records-header">
        <text class="records-title">我的记录</text>
        <text class="view-all" @click="viewAllRecords">查看全部</text>
      </view>
      <view class="records-list" v-if="recentRecords.length > 0">
        <view class="record-item" v-for="record in recentRecords" :key="record.recordId">
          <view class="record-info">
            <text class="record-prize">{{ record.prizeName }}</text>
            <text class="record-time">{{ formatTime(record.drawTime) }}</text>
          </view>
          <view class="record-status">
            <text class="status-text"
              :class="{ 'won': record.isWinner === '1', 'claimed': record.claimStatus === '1' }">
              {{ getRecordStatusText(record) }}
            </text>
          </view>
        </view>
      </view>
      <view class="no-records" v-else>
        <text>暂无抽奖记录</text>
      </view>
    </view>
  </view>
</template>

<script>
import { merchantApi, lotteryApi } from '@/utils/api.js'

export default {
  data() {
    return {
      merchantCode: '',
      tableNumber: '',
      merchantInfo: null,
      currentActivity: null,
      prizeList: [],
      remainingDraws: 0,
      recentRecords: [],
      isDrawing: false,
      userOpenid: '',
      hasDrawn: false,
      lotteryResult: null,
      // 九宫格相关数据
      gridItems: [],
      currentIndex: -1,
      animationTimer: null,
      animationSpeed: 100,
      animationCount: 0,
      targetIndex: -1
    }
  },

  onLoad(options) {
    this.merchantCode = options.merchantCode || '002'
    this.tableNumber = options.tableNumber || 'A002'

    this.initPage()
  },

  methods: {
    async initPage() {
      try {
        // 获取用户openid（实际项目中需要通过微信登录获取）
        // 为了测试重复抽奖限制，这里使用固定的测试用户ID
        this.userOpenid = 'test_user_001'

        // 加载商家信息
        await this.loadMerchantInfo()

        // 加载当前活动
        await this.loadCurrentActivity()

        // 检查用户抽奖状态
        await this.checkUserLotteryStatus()

        // 加载用户记录
        await this.loadUserRecords()

        // 初始化九宫格（只有在用户还没抽过奖时才初始化）
        if (!this.hasDrawn) {
          this.initGrid()
        }

      } catch (error) {
        console.error('页面初始化失败:', error)
        this.handleError(error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await merchantApi.getMerchantInfo(this.merchantCode)
        if (res.code === 200) {
          this.merchantInfo = res.data
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadCurrentActivity() {
      try {
        const res = await lotteryApi.getCurrentActivity(this.merchantCode)
        if (res.code === 200 && res.data) {
          this.currentActivity = res.data
          this.prizeList = JSON.parse(res.data.prizeConfig || '[]')

          // 获取剩余抽奖次数
          await this.loadRemainingDraws()
        }
      } catch (error) {
        console.error('获取活动信息失败:', error)
      }
    },

    async loadRemainingDraws() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getRemainingDraws(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          this.remainingDraws = res.data
        }
      } catch (error) {
        console.error('获取剩余次数失败:', error)
      }
    },

    async loadUserRecords() {
      try {
        const res = await lotteryApi.getUserRecords(this.userOpenid)
        if (res.code === 200) {
          this.recentRecords = res.data.slice(0, 5) // 只显示最近5条
        }
      } catch (error) {
        console.error('获取用户记录失败:', error)
      }
    },

    async checkUserLotteryStatus() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getUserLotteryStatus(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          const data = res.data
          this.hasDrawn = data.hasDrawn
          this.remainingDraws = data.remainingDraws

          if (data.hasDrawn && data.lotteryRecord) {
            this.lotteryResult = data.lotteryRecord
          }
        }
      } catch (error) {
        console.error('获取用户抽奖状态失败:', error)
      }
    },

    initGrid() {
      if (this.prizeList.length === 0) return

      // 创建九宫格数据，确保有8个奖品位置（中间是抽奖按钮）
      this.gridItems = []

      // 如果奖品不足8个，用"谢谢参与"填充
      const prizes = [...this.prizeList]
      while (prizes.length < 8) {
        prizes.push({
          name: '谢谢参与',
          prizeName: '谢谢参与',
          prizeType: 'thanks',
          probability: 0
        })
      }

      // 如果奖品超过8个，只取前8个
      if (prizes.length > 8) {
        prizes.splice(8)
      }

      // 九宫格布局：
      // 0  1  2
      // 7  4  3  (4是中心抽奖按钮)
      // 6  5  4
      // 需要创建9个位置，其中index=4是抽奖按钮，其他8个位置放奖品
      for (let i = 0; i < 9; i++) {
        if (i === 4) {
          // 中心位置是抽奖按钮，不需要奖品数据
          this.gridItems.push(null)
        } else {
          // 计算奖品索引：0,1,2,3对应prizes[0,1,2,3]，5,6,7,8对应prizes[4,5,6,7]
          const prizeIndex = i < 4 ? i : i - 1
          if (prizeIndex < prizes.length) {
            this.gridItems.push(prizes[prizeIndex])
          } else {
            this.gridItems.push({
              name: '谢谢参与',
              prizeName: '谢谢参与',
              prizeType: 'thanks',
              probability: 0
            })
          }
        }
      }
    },

    async startLottery() {
      if (this.isDrawing) return
      if (this.hasDrawn) {
        uni.showToast({
          title: '您已经抽过奖了',
          icon: 'none'
        })
        return
      }
      if (this.remainingDraws <= 0) {
        uni.showToast({
          title: '抽奖次数已用完',
          icon: 'none'
        })
        return
      }
      if (!this.currentActivity) {
        uni.showToast({
          title: '暂无可参与的活动',
          icon: 'none'
        })
        return
      }

      this.isDrawing = true

      try {
        const drawData = {
          activityId: this.currentActivity.activityId,
          userOpenid: this.userOpenid,
          userNickname: '用户' + this.userOpenid.slice(-4),
          userAvatar: '',
          tableId: null // 如果有桌台信息可以传入
        }

        const res = await lotteryApi.performDraw(drawData)
        if (res.code === 200) {
          const result = res.data

          // 找到中奖奖品在九宫格中的位置
          let targetIndex = this.gridItems.findIndex(item =>
            item && (item.prizeName === result.prizeName || item.name === result.prizeName)
          )

          // 如果没找到，默认停在第一个位置（跳过中心位置）
          if (targetIndex === -1 || targetIndex === 4) {
            targetIndex = 0
          }

          // 开始九宫格动画
          this.startGridAnimation(targetIndex, () => {
            this.showResult(result)
            this.isDrawing = false
            this.hasDrawn = true
            this.lotteryResult = result
            this.remainingDraws = 0
            this.loadUserRecords() // 刷新记录
          })

        } else {
          throw new Error(res.msg || '抽奖失败')
        }
      } catch (error) {
        this.isDrawing = false
        this.handleError(error)
      }
    },

    startGridAnimation(targetIndex, callback) {
      this.targetIndex = targetIndex
      this.animationCount = 0
      this.currentIndex = 0
      this.animationSpeed = 100

      // 九宫格动画顺序：跳过中心位置(index=4)
      // 顺序：0,1,2,3,5,6,7,8 (跳过4)
      const animationOrder = [0, 1, 2, 3, 5, 6, 7, 8]

      // 动画总圈数和最终位置
      const totalRounds = 3 // 转3圈
      const totalSteps = totalRounds * 8 + animationOrder.indexOf(targetIndex)

      const animate = () => {
        const orderIndex = this.animationCount % 8
        this.currentIndex = animationOrder[orderIndex]
        this.animationCount++

        // 动态调整速度，最后几步减速
        if (this.animationCount > totalSteps - 10) {
          this.animationSpeed = 200
        } else if (this.animationCount > totalSteps - 20) {
          this.animationSpeed = 150
        }

        if (this.animationCount >= totalSteps) {
          // 动画结束
          this.currentIndex = targetIndex
          clearTimeout(this.animationTimer)
          setTimeout(callback, 500) // 延迟500ms显示结果
        } else {
          // 继续动画
          this.animationTimer = setTimeout(animate, this.animationSpeed)
        }
      }

      animate()
    },

    showResult(result) {
      const isWinner = result.isWinner === '1'
      const title = isWinner ? '恭喜中奖！' : '很遗憾'
      const content = isWinner ?
        `您获得了：${result.prizeName}` :
        '谢谢参与，再接再厉！'

      uni.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: isWinner ? '去领取' : '确定',
        success: (res) => {
          if (res.confirm && isWinner) {
            // 跳转到领取页面
            uni.navigateTo({
              url: `/pages/claim/claim?recordId=${result.recordId}`
            })
          }
        }
      })
    },

    viewAllRecords() {
      uni.navigateTo({
        url: `/pages/records/records?userOpenid=${this.userOpenid}`
      })
    },

    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    getRecordStatusText(record) {
      if (record.isWinner === '0') {
        return '未中奖'
      } else if (record.claimStatus === '1') {
        return '已领取'
      } else {
        return '待领取'
      }
    },

    handleError(error) {
      let message = error.message || error.msg || '系统异常'

      // 检查是否是商家到期错误
      if (message.includes('过期') || message.includes('到期')) {
        uni.showModal({
          title: '系统提示',
          content: message,
          showCancel: false,
          confirmText: '我知道了'
        })
      } else {
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.lottery-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.merchant-header {
  text-align: center;
  margin-bottom: 60rpx;

  .merchant-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .activity-name {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.lottery-result-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .result-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    min-width: 500rpx;

    .result-icon {
      font-size: 120rpx;
      margin-bottom: 30rpx;
    }

    .result-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .result-prize {
      font-size: 32rpx;
      color: #ff4757;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .result-time {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 20rpx;
    }

    .result-status {
      font-size: 28rpx;

      .claimed {
        color: #2ed573;
      }

      text:not(.claimed) {
        color: #ff6b6b;
      }
    }
  }
}

.lottery-grid-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .grid-wrapper {
    width: 600rpx;
    height: 600rpx;
  }

  .lottery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8rpx;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .grid-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.active {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      transform: scale(1.05);
      box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);

      .prize-item {
        .prize-icon {
          animation: bounce 0.6s ease-in-out;
        }

        .prize-name {
          color: #fff;
          font-weight: bold;
        }
      }
    }

    &.center {
      background: linear-gradient(135deg, #667eea, #764ba2);
      cursor: pointer;

      &:hover {
        transform: scale(1.02);
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .prize-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 10rpx;

    .prize-icon {
      font-size: 48rpx;
      margin-bottom: 8rpx;
    }

    .prize-name {
      font-size: 24rpx;
      color: #333;
      line-height: 1.2;
      word-break: break-all;
    }
  }

  .center-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #fff;

    .center-text {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .remaining-text {
      font-size: 20rpx;
      opacity: 0.9;
    }
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10rpx);
  }

  60% {
    transform: translateY(-5rpx);
  }
}



.prize-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;

  .prize-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .prize-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .prize-item {
    flex: 1;
    min-width: 200rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
    padding: 20rpx;
    text-align: center;

    .prize-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .prize-probability {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.lottery-records {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;

  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .records-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .view-all {
      font-size: 26rpx;
      color: #667eea;
    }
  }

  .records-list {
    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .record-info {
        flex: 1;

        .record-prize {
          font-size: 28rpx;
          color: #333;
          display: block;
          margin-bottom: 5rpx;
        }

        .record-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .record-status {
        .status-text {
          font-size: 24rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          background: #f0f0f0;
          color: #666;

          &.won {
            background: #fff3cd;
            color: #856404;
          }

          &.claimed {
            background: #d4edda;
            color: #155724;
          }
        }
      }
    }
  }

  .no-records {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }
}
</style>